<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Create menu visibility rules to hide unwanted menus from User Creation Only group -->

        <!-- Rule to hide all Settings submenus except our custom Users menu -->
        <record id="rule_hide_settings_submenus" model="ir.rule">
            <field name="name">Hide Settings Submenus from User Creation Only</field>
            <field name="model_id" ref="base.model_ir_ui_menu"/>
            <field name="domain_force">[
                '|',
                ('parent_id', '!=', ref('base.menu_administration')),
                '|',
                ('id', '=', ref('base.menu_administration')),
                ('id', '=', ref('menu_users_user_creation_only'))
            ]</field>
            <field name="groups" eval="[(4, ref('group_user_creation_only'))]"/>
            <field name="perm_read">True</field>
            <field name="perm_write">False</field>
            <field name="perm_create">False</field>
            <field name="perm_unlink">False</field>
        </record>

    </data>
</odoo>
