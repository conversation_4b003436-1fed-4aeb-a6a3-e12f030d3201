# -*- coding: utf-8 -*-
{
    'name': 'User Creation Only Permission',
    'version': '********.0',
    'category': 'Administration',
    'summary': 'Restricted user creation permissions with Settings access',
    'description': """
User Creation Only Permission Module
====================================

This module creates a new security group "User Creation Only" with controlled functionality:

Features:
- Shows Settings menu with only Users section visible
- Create new users with Name and Email fields only
- Hides Access Rights tab completely
- Cannot modify or delete existing users
- Cannot access other Settings sections (Technical, General Settings, etc.)
- Cannot assign groups or permissions to users

Perfect for giving limited user management capabilities without full admin access.
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': ['base', 'base_setup'],
    'data': [
        'security/security_groups.xml',
        'security/ir.model.access.csv',
        'security/record_rules.xml',
        'views/res_users_views.xml',
        'views/menu_views.xml',
        'views/hide_menus.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}
