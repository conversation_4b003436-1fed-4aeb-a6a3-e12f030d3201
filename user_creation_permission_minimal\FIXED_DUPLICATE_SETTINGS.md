# إصلاح مشكلة ظهور قائمتين Settings

## المشكلة
كان يظهر للمستخدم قائمتان Settings:
1. Settings الأساسية (بأيقونة الترس) 
2. Settings مخصصة (بدون أيقونة)

## الحل المطبق

### 1. استخدام قائمة Settings الأساسية
بدلاً من إنشاء قائمة Settings جديدة، تم:
- إعطاء مجموعة "User Creation Only" صلاحية الوصول لقائمة Settings الأساسية
- إنشاء قائمة "Users" مخصصة تحت Settings الأساسية

### 2. إخفاء القوائم الفرعية غير المرغوبة
تم إنشاء قواعد لإخفاء جميع القوائم الفرعية تحت Settings عدا قائمة Users المخصصة:

```xml
<!-- في ملف hide_menus.xml -->
<record id="rule_hide_settings_submenus" model="ir.rule">
    <field name="domain_force">[
        '|',
        ('parent_id', '!=', ref('base.menu_administration')),
        '|',
        ('id', '=', ref('base.menu_administration')),
        ('id', '=', ref('menu_users_user_creation_only'))
    ]</field>
</record>
```

### 3. هيكل القوائم الجديد
```
Settings (الأساسية - بأيقونة الترس)
└── Users (مخصصة لمجموعة User Creation Only)
```

## الملفات المحدثة

### 1. `views/menu_views.xml`
- تم تبسيط الملف
- إعطاء صلاحية الوصول لقائمة Settings الأساسية
- إنشاء قائمة Users مخصصة تحتها

### 2. `views/hide_menus.xml` (جديد)
- قواعد إخفاء القوائم الفرعية غير المرغوبة
- استخدام ir.rule لتحديد القوائم المرئية

### 3. `security/record_rules.xml`
- إزالة القواعد المكررة
- الاحتفاظ بقواعد البيانات الأساسية فقط

### 4. `__manifest__.py`
- إضافة ملف hide_menus.xml للبيانات

## النتيجة المتوقعة

### للمستخدم مع مجموعة "User Creation Only":
- ✅ يرى قائمة Settings واحدة فقط (الأساسية بأيقونة الترس)
- ✅ عند فتح Settings يرى قائمة Users فقط
- ✅ لا يرى General Settings أو Technical أو أي قوائم أخرى
- ✅ يمكنه إنشاء مستخدمين جدد بحقلي Name و Email فقط

### للمستخدمين الآخرين:
- ✅ يرون قائمة Settings العادية بجميع الأقسام
- ✅ لا تأثير على وظائفهم العادية

## خطوات الاختبار

1. **إعادة تثبيت الموديول**:
   ```
   - إلغاء تثبيت الموديول
   - إعادة تثبيته
   ```

2. **إنشاء مستخدم اختبار**:
   ```
   - إنشاء مستخدم جديد
   - إعطاؤه مجموعة "User Creation Only" فقط
   - عدم إعطاؤه أي مجموعات إدارية أخرى
   ```

3. **اختبار الوظائف**:
   ```
   - تسجيل الدخول بالمستخدم الجديد
   - التحقق من ظهور قائمة Settings واحدة فقط
   - التحقق من ظهور قائمة Users فقط تحت Settings
   - اختبار إنشاء مستخدم جديد
   ```

## ملاحظات مهمة

- تأكد من عدم إعطاء المستخدم أي مجموعات إدارية أخرى
- المستخدم يجب أن يكون له مجموعة "User Creation Only" فقط
- في حالة وجود مشاكل، تحقق من logs الخادم للأخطاء

الآن يجب أن تظهر قائمة Settings واحدة فقط مع محتوى محدود! 🎯
