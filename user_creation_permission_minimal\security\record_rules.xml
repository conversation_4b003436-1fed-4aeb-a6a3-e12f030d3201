<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Record Rule: User Creation Only can only create partners for users -->
        <record id="user_creation_only_partner_rule" model="ir.rule">
            <field name="name">User Creation Only: Partner Creation Rule</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="domain_force">[('is_company', '=', False)]</field>
            <field name="groups" eval="[(4, ref('group_user_creation_only'))]"/>
            <field name="perm_read">True</field>
            <field name="perm_write">True</field>
            <field name="perm_create">True</field>
            <field name="perm_unlink">False</field>
        </record>

        <!-- Record Rule: User Creation Only can only read users, not modify existing ones -->
        <record id="user_creation_only_users_rule" model="ir.rule">
            <field name="name">User Creation Only: Users Read Rule</field>
            <field name="model_id" ref="base.model_res_users"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_user_creation_only'))]"/>
            <field name="perm_read">True</field>
            <field name="perm_write">False</field>
            <field name="perm_create">True</field>
            <field name="perm_unlink">False</field>
        </record>



    </data>
</odoo>
