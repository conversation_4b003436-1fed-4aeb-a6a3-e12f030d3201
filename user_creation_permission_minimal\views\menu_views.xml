<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Give User Creation Only group access to the main Settings menu -->
        <record id="base.menu_administration" model="ir.ui.menu">
            <field name="groups_id" eval="[(4, ref('group_user_creation_only'))]"/>
        </record>

        <!-- Custom Users menu item under main Settings for User Creation Only group -->
        <menuitem id="menu_users_user_creation_only"
                  name="Users"
                  parent="base.menu_administration"
                  action="base.action_res_users"
                  sequence="1"
                  groups="group_user_creation_only"/>

    </data>
</odoo>
